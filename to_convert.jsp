  <body onload="initForm();" onfocus="onFocusForm();" >
    <jsp:include page="header.jsp">
      <jsp:param name="PARAM" value="商品グループ登録（詳細）（AUT90042）" />
    </jsp:include>
    <html:form action="syohinGroupDetailUpdate" method="post" style="width: 995px; margin: 0px auto;">
      <div id="term" style="display: table; width: 100%; text-align: left; border-left: solid 1px #c0c0c0; ">
        <fvo:span property='term_detail_area1' >
          <fvo:span property='bunrui1_area' >
            <table class="bunrui" style="width: 49.99%;">
              <tr>
                <th><fvo:span property='bunrui1_title' ></fvo:span></th>
                <td>
                   <fvo:text style="ime-mode: disabled; width:110px;"  property='bunrui1_cd' styleClass="readOnly" />
                  <fvo:text size="20" property='bunrui1_na' style="width:110px;" styleClass="readOnly" />
                </td>
              </tr>
            </table>
          </fvo:span>
          <fvo:span property='bunrui2_area' >
            <table class="bunrui" style="width: 49.99%;">
              <tr>
                <th><fvo:span property='bunrui2_title' ></fvo:span></th>
                <td>
                  <fvo:text style="ime-mode: disabled; width:110px;" property='bunrui2_cd' styleClass="readOnly" />
                  <fvo:text size="20" property='bunrui2_na' style="width:110px;" styleClass="readOnly" />
                </td>
              </tr>
            </table>
          </fvo:span>
          <fvo:span property='bunrui3_area' >
            <table class="bunrui" style="width: 49.99%;">
              <tr>
                <th><fvo:span property='bunrui3_title' ></fvo:span></th>
                <td>
                  <fvo:text style="ime-mode: disabled; width:110px;" property='bunrui3_cd' styleClass="readOnly" />
                  <fvo:text size="20" property='bunrui3_na'  style="width:110px;" styleClass="readOnly" />
                </td>
              </tr>
            </table>
          </fvo:span>
          <fvo:span property='bunrui4_area' >
            <table class="bunrui" style="width: 49.99%;">
              <tr>
                <th><fvo:span property='bunrui4_title' ></fvo:span></th>
                <td>
                  <fvo:text style="ime-mode: disabled; width:110px;" property='bunrui4_cd' styleClass="readOnly" />
                  <fvo:text size="20" property='bunrui4_na' style="width:110px;" styleClass="readOnly" />
                </td>
              </tr>
            </table>
          </fvo:span>
          <fvo:span property='bunrui5_area' >
            <table class="bunrui" style="width: 49.99%;">
              <tr>
                <th><fvo:span property='bunrui5_title' ></fvo:span></th>
                <td>
                  <fvo:text style="ime-mode: disabled; width:110px;" property='bunrui5_cd' styleClass="readOnly" />
                  <fvo:text size="20" property='bunrui5_na' style="width:110px;" styleClass="readOnly" />
              </tr>
            </table>
          </fvo:span>
          <table class="bunrui" style="width: 49.99%;">
            <tr>
              <th>商品グループ</th>
              <td>
                <fvo:text size="12" style="width:70px;" maxlength="4" property='syohin_group_cd' styleClass="readOnly" />
                <fvo:text size="20" style="width:110px;" property='syohin_group_na' styleClass="readOnly" />
              </td>
            </tr>
          </table>
        </fvo:span>
      </div>
      <br/>
      <table align="center">
        <tr>
          <td align="center">
            <fvo:span style="display: inline; " property='message' ></fvo:span>
          </td>
        </tr>
      </table>
      <br />
      <!-- =================================================== -->
      <div align="center">
        <table class="list">
          <thead>
            <tr height="70">
              <th style="width: 100px">入力商品追加</th>
               <td style="width: 220px">
               	<center>
                <fvo:textarea property='syohin_cd_list' style="width:117px; height:76px;"></fvo:textarea>
                <fvo:button value="クリア"  style="vertical-align:bottom; margin-bottom:1px;" onclick="this_getElementById('syohin_cd_list').value=''" property='syohin_clear' styleClass="clearButton"  />
                </center>
              </td>
              <td align="center" style="width: 50px">
                <fvo:submit style="width:117px; height:32px;" value="&emsp;追&emsp;加&emsp;" onclick="return doAction(this);" property='add_button' styleClass="controlButton" />
              </td>
              <th style="width: 100px">選択商品削除</th>
              <td align="left" style="width: 220px">
                選択チェックボックスを<br>
                <fvo:submit style="width:76px; height:21px;" value="全て選択" onclick="return doAction(this);" property='select_all' styleClass="" />
                <fvo:submit style="width:115px; height:21px;" value="全て選択解除" onclick="return doAction(this);" property='deselect_all' styleClass="" />
                <br>
              </td>
              <td align="center" style="width: 50px">
                <fvo:submit style="width:117px; height:32px;" value="&emsp;削&emsp;除&emsp;" onclick="return doAction(this);" property='set_button' styleClass="controlButton" />
              </td>
            </tr>
          </thead>
        </table>
      </div>
      <br />
      <div align="center">
        <fvo:span property='result_area' >
          <table>
            <tr><td style="text-align: left;">
              <table class="list" style="text-align: center;">
                <thead>
                  <tr style="height:18px;">
                    <th style="width:40px;">選択</th>
                    <th style="width:120px;">商品コード</th>
                    <th style="width:390px;">商品名</th>
                  </tr>
                </thead>
                <tbody></tbody>
              </table>
              <div id="scrollResult" class="scroll" style="height: 300px;" >
                <table class="list">
                  <logic:iterate name="SyohinGroupDetailUpdateForm" property='result_rows' id="result_rows" indexId="result_rowsindex" type="jp.co.vinculumjapan.mdware.ordercommon.struts.form.rows.SyohinGroupDetailUpdateFormResult_rowsBean" >
				<tr style="height:23px;">
                    <td align="center" style="width:40px;"><fvo:checkbox property='<%="result_rows[" + result_rowsindex + "].select"%>' /></td>
                    <td style="width:120px;"><fvo:span property='<%="result_rows[" + result_rowsindex + "].syohin_cd"%>' ></fvo:span></td>
                    <td style="width:390px;"><fvo:span property='<%="result_rows[" + result_rowsindex + "].syohin_na"%>' ></fvo:span></td>
                  </tr>
				</logic:iterate>
                </table>
              </div>
            </td></tr>
          </table>
        </fvo:span>
        <fvo:submit value="&emsp;戻&emsp;る&emsp;" onclick="return doAction(this);" property='return_button' styleClass="controlButton" />
      </div>
      <br />
      <jsp:include page="footer.jsp" />
  </html:form></body>